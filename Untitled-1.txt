
> Configure project :launcher
WARNING:The option setting 'android.bundle.enableUncompressedNativeLibs=false' is deprecated.
The current default is 'true'.
It will be removed in version 8.0 of the Android Gradle plugin.
You can add the following to your build.gradle instead:
android {
    packagingOptions {
        jniLibs {
            useLegacyPackaging = true
        }
    }
}
WARNING:The option setting 'android.aapt2FromMavenOverride=F:\AndroidEnv\AndroidPlayer-bobo-tw\SDK\build-tools\35.0.0\aapt2.exe' is experimental.
WARNING:Using flatDir should be avoided because it doesn't support any meta-data formats.

> Configure project :unityLibrary
WARNING:Using flatDir should be avoided because it doesn't support any meta-data formats.

> Configure project :unityLibrary:FirebaseApp.androidlib
WARNING:Using flatDir should be avoided because it doesn't support any meta-data formats.
WARNING:We recommend using a newer Android Gradle plugin to use compileSdk = 35

This Android Gradle plugin (7.2.0) was tested up to compileSdk = 32

This warning can be suppressed by adding
    android.suppressUnsupportedCompileSdk=35
to this project's gradle.properties

The build will continue, but you are strongly encouraged to update your project to
use a newer Android Gradle Plugin that has been tested with compileSdk = 35

> Task :launcher:preBuild UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:preBuild UP-TO-DATE
> Task :unityLibrary:preBuild UP-TO-DATE
> Task :unityLibrary:preReleaseBuild UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:preReleaseBuild UP-TO-DATE
> Task :launcher:preReleaseBuild UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseRenderscript NO-SOURCE
> Task :unityLibrary:packageReleaseRenderscript NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseRenderscript NO-SOURCE
> Task :unityLibrary:compileReleaseRenderscript NO-SOURCE
> Task :launcher:generateReleaseResValues UP-TO-DATE
> Task :unityLibrary:generateReleaseResValues UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseResValues UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseResources UP-TO-DATE
> Task :unityLibrary:generateReleaseResources UP-TO-DATE
> Task :launcher:compileReleaseRenderscript NO-SOURCE
> Task :launcher:generateReleaseResources UP-TO-DATE
> Task :launcher:createReleaseCompatibleScreenManifests UP-TO-DATE
> Task :launcher:extractDeepLinksRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseResources UP-TO-DATE
> Task :unityLibrary:packageReleaseResources UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:extractDeepLinksRelease UP-TO-DATE
> Task :unityLibrary:extractDeepLinksRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:processReleaseManifest UP-TO-DATE
> Task :unityLibrary:processReleaseManifest UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseLibraryResources UP-TO-DATE
> Task :unityLibrary:compileReleaseLibraryResources UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseAidl NO-SOURCE
> Task :unityLibrary:writeReleaseAarMetadata UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:writeReleaseAarMetadata UP-TO-DATE
> Task :unityLibrary:compileReleaseAidl NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:parseReleaseLocalResources UP-TO-DATE
> Task :unityLibrary:parseReleaseLocalResources UP-TO-DATE
> Task :unityLibrary:generateReleaseBuildConfig UP-TO-DATE
> Task :unityLibrary:javaPreCompileRelease UP-TO-DATE
> Task :unityLibrary:mergeReleaseShaders UP-TO-DATE
> Task :unityLibrary:compileReleaseShaders NO-SOURCE
> Task :unityLibrary:generateReleaseAssets UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseRFile UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseBuildConfig UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:javaPreCompileRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseJavaWithJavac UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibCompileToJarRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibRuntimeToJarRelease UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseShaders UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseShaders NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseAssets UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseAssets UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:processReleaseJavaRes NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibResRelease NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseJniLibFolders UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseNativeLibs NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:copyReleaseJniLibsProjectOnly UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:prepareReleaseArtProfile UP-TO-DATE
> Task :unityLibrary:packageReleaseAssets
> Task :launcher:checkReleaseDuplicateClasses UP-TO-DATE
> Task :launcher:mergeReleaseResources UP-TO-DATE
> Task :launcher:mapReleaseSourceSetPaths UP-TO-DATE
> Task :launcher:processReleaseMainManifest UP-TO-DATE
> Task :launcher:processReleaseManifest UP-TO-DATE
> Task :launcher:processApplicationManifestReleaseForBundle UP-TO-DATE
> Task :unityLibrary:generateReleaseRFile UP-TO-DATE
> Task :launcher:bundleReleaseResources UP-TO-DATE
> Task :launcher:compileReleaseAidl NO-SOURCE
> Task :launcher:generateReleaseBuildConfig UP-TO-DATE
> Task :unityLibrary:compileReleaseJavaWithJavac UP-TO-DATE
> Task :launcher:javaPreCompileRelease UP-TO-DATE
> Task :unityLibrary:bundleLibCompileToJarRelease UP-TO-DATE
> Task :unityLibrary:bundleLibRuntimeToJarRelease UP-TO-DATE
> Task :unityLibrary:processReleaseJavaRes NO-SOURCE
> Task :unityLibrary:bundleLibResRelease NO-SOURCE
> Task :launcher:checkReleaseAarMetadata UP-TO-DATE
> Task :launcher:processReleaseManifestForPackage UP-TO-DATE
> Task :unityLibrary:mergeReleaseJniLibFolders
> Task :launcher:processReleaseResources UP-TO-DATE
> Task :launcher:compileReleaseJavaWithJavac UP-TO-DATE
> Task :unityLibrary:prepareReleaseArtProfile UP-TO-DATE
> Task :unityLibrary:mergeReleaseNativeLibs
> Task :unityLibrary:copyReleaseJniLibsProjectOnly

> Task :launcher:dexBuilderRelease FAILED

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.4.2/userguide/command_line_interface.html#sec:command_line_warnings
54 actionable tasks: 5 executed, 49 up-to-date

UnityEngine.GUIUtility:ProcessEvent (int,intptr,bool&)








Starting a Gradle Daemon, 2 incompatible Daemons could not be reused, use --status for details

> Configure project :launcher
WARNING:The option setting 'android.bundle.enableUncompressedNativeLibs=false' is deprecated.
The current default is 'true'.
It will be removed in version 8.0 of the Android Gradle plugin.
You can add the following to your build.gradle instead:
android {
    packagingOptions {
        jniLibs {
            useLegacyPackaging = true
        }
    }
}
WARNING:The option setting 'android.aapt2FromMavenOverride=F:\AndroidEnv\AndroidPlayer-bobo-tw\SDK\build-tools\35.0.0\aapt2.exe' is experimental.
WARNING:Using flatDir should be avoided because it doesn't support any meta-data formats.

> Configure project :unityLibrary
WARNING:Using flatDir should be avoided because it doesn't support any meta-data formats.

> Configure project :unityLibrary:FirebaseApp.androidlib
WARNING:Using flatDir should be avoided because it doesn't support any meta-data formats.
WARNING:We recommend using a newer Android Gradle plugin to use compileSdk = 35

This Android Gradle plugin (7.2.0) was tested up to compileSdk = 32

This warning can be suppressed by adding
    android.suppressUnsupportedCompileSdk=35
to this project's gradle.properties

The build will continue, but you are strongly encouraged to update your project to
use a newer Android Gradle Plugin that has been tested with compileSdk = 35

> Task :unityLibrary:FirebaseApp.androidlib:preBuild UP-TO-DATE
> Task :unityLibrary:preBuild UP-TO-DATE
> Task :launcher:preBuild UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:preReleaseBuild UP-TO-DATE
> Task :launcher:preReleaseBuild UP-TO-DATE
> Task :unityLibrary:preReleaseBuild UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseRenderscript NO-SOURCE
> Task :unityLibrary:packageReleaseRenderscript NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseRenderscript NO-SOURCE
> Task :unityLibrary:compileReleaseRenderscript NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseResValues
> Task :unityLibrary:generateReleaseResValues
> Task :launcher:generateReleaseResValues
> Task :unityLibrary:generateReleaseResources
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseResources
> Task :launcher:compileReleaseRenderscript NO-SOURCE
> Task :launcher:generateReleaseResources
> Task :launcher:createReleaseCompatibleScreenManifests
> Task :launcher:extractDeepLinksRelease
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseResources
> Task :unityLibrary:FirebaseApp.androidlib:extractDeepLinksRelease
> Task :unityLibrary:packageReleaseResources
> Task :unityLibrary:extractDeepLinksRelease
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseAidl NO-SOURCE
> Task :unityLibrary:compileReleaseAidl NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseLibraryResources
> Task :unityLibrary:FirebaseApp.androidlib:writeReleaseAarMetadata
> Task :unityLibrary:writeReleaseAarMetadata
> Task :unityLibrary:compileReleaseLibraryResources
> Task :unityLibrary:FirebaseApp.androidlib:processReleaseManifest

> Task :unityLibrary:processReleaseManifest
E:\ProjectsUnity\MaJiangClassic-zplay\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:35:5-40 Warning:
	property was tagged at AndroidManifest.xml:35 to remove other declarations but no other declaration present

> Task :unityLibrary:generateReleaseBuildConfig
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseBuildConfig
> Task :unityLibrary:javaPreCompileRelease
> Task :unityLibrary:FirebaseApp.androidlib:javaPreCompileRelease
> Task :unityLibrary:mergeReleaseShaders
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseShaders
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseShaders NO-SOURCE
> Task :unityLibrary:compileReleaseShaders NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseAssets UP-TO-DATE
> Task :unityLibrary:generateReleaseAssets UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseAssets
> Task :unityLibrary:FirebaseApp.androidlib:processReleaseJavaRes NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibResRelease NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseJniLibFolders
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseNativeLibs NO-SOURCE
> Task :unityLibrary:parseReleaseLocalResources
> Task :unityLibrary:FirebaseApp.androidlib:parseReleaseLocalResources
> Task :unityLibrary:FirebaseApp.androidlib:copyReleaseJniLibsProjectOnly
> Task :unityLibrary:FirebaseApp.androidlib:prepareReleaseArtProfile UP-TO-DATE
> Task :unityLibrary:packageReleaseAssets
> Task :unityLibrary:processReleaseJavaRes NO-SOURCE
> Task :unityLibrary:bundleLibResRelease NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseRFile
> Task :launcher:checkReleaseDuplicateClasses
> Task :unityLibrary:mergeReleaseJniLibFolders
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseJavaWithJavac
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibRuntimeToJarRelease
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibCompileToJarRelease
> Task :unityLibrary:prepareReleaseArtProfile UP-TO-DATE
> Task :unityLibrary:generateReleaseRFile
> Task :unityLibrary:mergeReleaseNativeLibs

> Task :launcher:processReleaseMainManifest
[com.pangle.global:pag-sdk:7.1.0.8] C:\Users\<USER>\.gradle\caches\transforms-3\096575eac252d287d2d200799fb3efab\transformed\jetified-pag-sdk-7.1.0.8\AndroidManifest.xml Warning:
	Namespace 'com.bytedance' used in: com.pangle.global:pag-sdk:7.1.0.8, com.pangle.global:pag-sdk-ad:unfat-7108-20250429200228.
E:\ProjectsUnity\MaJiangClassic-zplay\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:98:9-44 Warning:
	property was tagged at AndroidManifest.xml:98 to remove other declarations but no other declaration present

> Task :launcher:processReleaseManifest
> Task :launcher:processApplicationManifestReleaseForBundle
> Task :launcher:compileReleaseAidl NO-SOURCE
> Task :launcher:generateReleaseBuildConfig
> Task :launcher:javaPreCompileRelease
> Task :launcher:mergeReleaseShaders
> Task :launcher:compileReleaseShaders NO-SOURCE
> Task :launcher:generateReleaseAssets UP-TO-DATE
> Task :launcher:checkReleaseAarMetadata
> Task :launcher:mergeReleaseAssets
> Task :launcher:processReleaseJavaRes NO-SOURCE
> Task :launcher:mergeReleaseJniLibFolders
> Task :launcher:writeReleaseAppMetadata
> Task :launcher:collectReleaseDependencies
> Task :launcher:configureReleaseDependencies
> Task :launcher:parseReleaseIntegrityConfig UP-TO-DATE
> Task :launcher:validateSigningRelease
> Task :launcher:processReleaseManifestForPackage
> Task :launcher:mergeReleaseArtProfile
> Task :launcher:mergeReleaseJavaResource

> Task :unityLibrary:compileReleaseJavaWithJavac

> Task :unityLibrary:bundleLibRuntimeToJarRelease
> Task :unityLibrary:bundleLibCompileToJarRelease
> Task :launcher:mergeReleaseResources
> Task :launcher:mapReleaseSourceSetPaths
> Task :unityLibrary:copyReleaseJniLibsProjectOnly
> Task :launcher:mergeReleaseNativeLibs

> Task :launcher:stripReleaseDebugSymbols
[CXX5106] NDK was located by using ndk.dir property. This method is deprecated and will be removed in a future release. Please use android.ndkVersion or android.ndkPath in build.gradle to specify the NDK to use. https://developer.android.com/r/studio-ui/ndk-dir

> Task :launcher:bundleReleaseResources
> Task :launcher:processReleaseResources
> Task :launcher:compileReleaseJavaWithJavac

> Task :launcher:extractReleaseNativeSymbolTables
[CXX5106] NDK was located by using ndk.dir property. This method is deprecated and will be removed in a future release. Please use android.ndkVersion or android.ndkPath in build.gradle to specify the NDK to use. https://developer.android.com/r/studio-ui/ndk-dir

> Task :launcher:dexBuilderRelease

> Task :launcher:dexBuilderRelease FAILED

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.5/userguide/command_line_interface.html#sec:command_line_warnings
67 actionable tasks: 64 executed, 3 up-to-date

UnityEngine.GUIUtility:ProcessEvent (int,intptr,bool&)
