<?xml version="1.0" encoding="utf-8"?>
<component size="720,1280" designImage="ui://d0htg1xdt5i141" designImageAlpha="100">
  <displayList>
    <loader id="n20_ss2k" name="n20" xy="0,0" size="720,1280" aspect="true" url="ui://d0htg1xdss2k3i" align="center" vAlign="middle" fill="scaleNoBorder">
      <relation target="" sidePair="width-width,height-height"/>
    </loader>
    <image id="n26_t5i1" name="n26" src="t5i142" fileName="images/logo.png" pkg="dd926aok" xy="159,266">
      <relation target="" sidePair="center-center,middle-middle"/>
    </image>
    <component id="n33_rnrt" name="btnDailyChallenge" src="rnrt4o" fileName="BtnDailyChallenge.xml" xy="187,1000" size="344,93" group="n6_m6gv" visible="false"/>
    <component id="n29_qlss" name="btnStart" src="inpg6d" fileName="BtnStart.xml" xy="172,983" size="375,128" group="n6_m6gv"/>
    <group id="n6_m6gv" name="n6" xy="172,983" size="375,128" advanced="true">
      <relation target="" sidePair="center-center,middle-middle"/>
    </group>
    <component id="n25_t5i1" name="btnSetting" src="p6dvo" fileName="IconButton.xml" pkg="dzm3l2gb" xy="30,60" size="69,79" group="n13_m6gv">
      <Button icon="ui://d0htg1xdss2k3j"/>
    </component>
    <component id="n11_m6gv" name="coinBar" src="rckv2v" fileName="CoinBar.xml" pkg="dzm3l2gb" xy="453,96" group="n13_m6gv"/>
    <component id="n12_m6gv" name="heartBar" src="g23932" fileName="HeartBar.xml" xy="227,93" group="n13_m6gv"/>
    <group id="n13_m6gv" name="n13" xy="30,51" size="528,87" advanced="true">
      <relation target="" sidePair="left-left,top-top"/>
    </group>
    <component id="n18_aj2j" name="levelBar" src="aj2j39" fileName="BoxBar.xml" xy="493,198">
      <relation target="" sidePair="center-center,top-top"/>
      <Button title="Level Chest"/>
    </component>
    <component id="n19_evzc" name="btnPig" src="ufgj4r" fileName="BtnPig.xml" xy="205,195" pivot="0.5,0.5" anchor="true">
      <relation target="" sidePair="center-center,top-top"/>
      <Button title="Piggy Bank"/>
    </component>
    <text id="n30_tn6z" name="txtVersion" xy="26,18" size="206,34" alpha="0.1" fontSize="20" color="#ffffff" vAlign="middle" autoSize="shrink" singleLine="true" autoClearText="true" text="1.1.0"/>
    <component id="n32_rnrt" name="btnRank" src="rnrt4n" fileName="BtnRank.xml" xy="530,812" visible="false">
      <relation target="" sidePair="middle-middle,right-right"/>
    </component>
    <component id="n39_mth2" name="btnCloseAd" src="p6dvo" fileName="IconButton.xml" pkg="dzm3l2gb" xy="653,1120" size="50,51">
      <relation target="" sidePair="right-right,bottom-bottom"/>
      <Button icon="ui://d0htg1xdoowk4s"/>
    </component>
    <component id="n40_mth2" name="btnShop" src="p6dvo" fileName="IconButton.xml" pkg="dzm3l2gb" xy="19,287" size="127,129">
      <relation target="" sidePair="left-left,top-top"/>
      <Button icon="ui://d0htg1xdss2k3n"/>
    </component>
    <component id="n42_xfyl" name="btnRemoveAd" src="nhny11" fileName="IconAutoSizeButton.xml" pkg="dzm3l2gb" xy="20,438" size="127,129">
      <relation target="" sidePair="left-left,top-top"/>
      <Button icon="ui://d0htg1xdxfyl4u"/>
    </component>
    <loader id="n43_xfyl" name="n43" xy="104,1200" size="511,80" aspect="true" touchable="false" url="ui://d0htg1xdxfyl4t" align="center" vAlign="middle" fill="scale" clearOnPublish="true">
      <relation target="" sidePair="center-center,bottomext-bottom"/>
    </loader>
  </displayList>
</component>